{"name": "izy-mercado", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite", "build": "vite build", "serve": "serve -s dist", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"axios": "^1.7.2", "lucide-react": "^0.462.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.2.1", "react-router-dom": "^6.23.0", "react-swipeable": "^7.0.2"}, "devDependencies": {"@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.38", "serve": "^14.2.3", "tailwindcss": "^3.4.3", "typescript": "^5.8.3", "vite": "^7.0.4", "vite-plugin-svgr": "^4.3.0"}}