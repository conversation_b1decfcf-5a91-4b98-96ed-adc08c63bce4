{"name": "serve-handler", "version": "6.1.6", "description": "The routing foundation of `serve`", "main": "src/index.js", "scripts": {"test": "yarn run test-lint && yarn run test-integration", "test-lint": "zeit-eslint --ext .jsx,.js .", "test-integration": "jest --forceExit test/integration.test.js", "lint-staged": "git diff --diff-filter=ACMRT --cached --name-only '*.js' '*.jsx' | xargs zeit-eslint", "build-views": "dottojs -s ./src -d ./src", "prepublishOnly": "yarn run build-views"}, "repository": "vercel/serve-handler", "keywords": ["static", "deployment", "server"], "author": "leo", "license": "MIT", "files": ["src/index.js", "src/glob-slash.js", "src/directory.js", "src/error.js"], "devDependencies": {"@zeit/eslint-config-node": "0.2.13", "@zeit/git-hooks": "0.1.4", "commander": "2.15.1", "dot": "1.1.3", "eslint": "6.1.0", "fs-extra": "6.0.1", "jest": "29.7.0", "micro": "9.3.2", "node-fetch": "2.6.1", "request": "2.87.0", "sleep-promise": "6.0.0", "test-listen": "1.1.0"}, "eslintConfig": {"extends": ["@zeit/eslint-config-node"], "env": {"jest": true}}, "eslintIgnore": ["error.js", "directory.js", "coverage"], "git": {"pre-commit": "lint-staged"}, "dependencies": {"bytes": "3.0.0", "content-disposition": "0.5.2", "mime-types": "2.1.18", "minimatch": "3.1.2", "path-is-inside": "1.0.2", "path-to-regexp": "3.3.0", "range-parser": "1.2.0"}, "packageManager": "yarn@1.22.19+sha1.4ba7fc5c6e704fce2066ecbfb0b0d8976fe62447"}