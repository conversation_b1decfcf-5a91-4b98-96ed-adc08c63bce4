[![React Swipeable — Formidable, We build the modern web](https://raw.githubusercontent.com/FormidableLabs/react-swipeable/main/react-swipeable-Hero.png)](https://formidable.com/open-source/)

React swipe event handler hook

[![npm downloads](https://img.shields.io/npm/dm/react-swipeable.svg)](https://www.npmjs.com/package/react-swipeable) [![npm version](https://img.shields.io/npm/v/react-swipeable.svg)](https://www.npmjs.com/package/react-swipeable) [![build status](https://github.com/FormidableLabs/react-swipeable/actions/workflows/ci.yml/badge.svg)](https://github.com/FormidableLabs/react-swipeable/actions) [![gzip size](https://badgen.net/bundlephobia/minzip/react-swipeable)](https://bundlephobia.com/result?p=react-swipeable) [![maintenance status](https://img.shields.io/badge/maintenance-active-green.svg)](https://github.com/FormidableLabs/react-swipeable#maintenance-status)

[![Edit react-swipeable image carousel](https://assets.codesandbox.io/github/button-edit-lime.svg)](https://codesandbox.io/s/github/FormidableLabs/react-swipeable/tree/main/examples?file=/app/SimpleCarousel/Carousel.tsx)

Visit the [Docs site](https://commerce.nearform.com/open-source/react-swipeable) for information on [usage](https://commerce.nearform.com/open-source/react-swipeable/docs/usage), [api](https://commerce.nearform.com/open-source/react-swipeable/api), and [demos](https://commerce.nearform.com/open-source/react-swipeable/docs/demo).

## License

[MIT]((./LICENSE))

## Contributing

Please see our [contributions guide](./CONTRIBUTING.md).

### Maintainers
[Project Maintenance](./CONTRIBUTING.md#project-maintainers)

## Maintenance Status

**Active:** Formidable is actively working on this project, and we expect to continue for work for the foreseeable future. Bug reports, feature requests and pull requests are welcome.
