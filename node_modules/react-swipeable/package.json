{"name": "react-swipeable", "version": "7.0.2", "description": "React Swipe event handler hook", "source": "./src/index.ts", "main": "./lib/index.js", "module": "es/index.js", "types": "./es/index.d.ts", "unpkg": "dist/react-swipeable.js", "sideEffects": false, "scripts": {"build": "yarn run build:types && yarn run build:outputs", "build:outputs": "rollup -c", "build:types": "tsc --project tsconfig.types.json", "check:ci": "yarn run prettier && yarn run lint && yarn run test:unit", "clean": "<PERSON><PERSON>f lib dist es", "examples:build": "cd ./examples && yarn && yarn build", "examples:build:publish": "yarn run examples:build && rimraf examples/node_modules && gh-pages -d examples", "prettier": "prettier --check src __tests__", "format": "prettier --write src __tests__", "lint": "eslint . --ext .ts,.tsx", "prebuild": "yarn run clean", "prepare": "yarn run build", "pretest": "yarn run prettier && yarn run lint", "preversion": "yarn test", "size": "size-limit", "start:examples": "cd ./examples && yarn && yarn start", "start:examples:local": "cd ./examples && yarn && yarn start:dev:local", "test": "yarn run test:unit && yarn run build && yarn run size", "test:unit": "jest", "test:unit:watch": "jest --watch", "test:cover": "jest --coverage"}, "size-limit": [{"limit": "1.7 KB", "path": "lib/index.js"}, {"limit": "1.8 KB", "path": "dist/react-swipeable.js"}, {"limit": "1.8 KB", "path": "es/index.js"}], "jest": {"preset": "ts-jest", "testEnvironment": "jsdom", "testMatch": ["<rootDir>/__tests__/**/*.(test|spec).ts?(x)"]}, "keywords": ["react swipe", "react touch", "react hook", "touch", "swipe", "swipeable", "react", "hook"], "authors": ["<PERSON> <<EMAIL>> (https://github.com/goatslacker)", "<PERSON> <brian<PERSON><PERSON>@gmail.com> (https://github.com/hartzis)"], "repository": {"type": "git", "url": "https://github.com/FormidableLabs/react-swipeable.git"}, "bugs": {"url": "https://github.com/FormidableLabs/react-swipeable/issues"}, "homepage": "https://github.com/FormidableLabs/react-swipeable", "files": ["dist", "es", "lib", "src"], "license": "MIT", "devDependencies": {"@changesets/cli": "^2.26.1", "@rollup/plugin-typescript": "^8.3.0", "@size-limit/preset-small-lib": "^7.0.8", "@svitejs/changesets-changelog-github-compact": "^0.1.1", "@testing-library/react": "^13.0.1", "@types/jest": "^27.4.1", "@types/react": "^18.0.8", "@types/react-dom": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.20.0", "@typescript-eslint/parser": "^5.20.0", "coveralls": "^3.0.3", "eslint": "^7.18.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-react": "^7.29.4", "eslint-plugin-react-hooks": "^4.4.0", "gh-pages": "^3.0.0", "jest": "^27.5.1", "prettier": "^2.0.5", "react": "^18.1.0", "react-dom": "^18.1.0", "rimraf": "^3.0.2", "rollup": "^2.60.0", "size-limit": "^7.0.8", "ts-jest": "^27.1.4", "typescript": "^4.6.3"}, "peerDependencies": {"react": "^16.8.3 || ^17 || ^18 || ^19.0.0 || ^19.0.0-rc"}, "publishConfig": {"provenance": true}}