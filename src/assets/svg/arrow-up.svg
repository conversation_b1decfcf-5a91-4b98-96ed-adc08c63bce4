<svg width="62" height="62" viewBox="0 0 62 62" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_2159_1041)">
<rect x="10" y="6" width="42" height="42" rx="21" fill="white"/>
<path d="M31 20V34" stroke="url(#paint0_linear_2159_1041)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M25.999 25L31 19.999L36.001 25" stroke="url(#paint1_linear_2159_1041)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<filter id="filter0_d_2159_1041" x="0" y="0" width="62" height="62" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.317647 0 0 0 0 0.301961 0 0 0 0 0.309804 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2159_1041"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2159_1041" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2159_1041" x1="30.5" y1="27" x2="31.5" y2="27" gradientUnits="userSpaceOnUse">
<stop stop-color="#B771E5"/>
<stop offset="1" stop-color="#ED9292"/>
</linearGradient>
<linearGradient id="paint1_linear_2159_1041" x1="25.999" y1="22.4995" x2="36.001" y2="22.4995" gradientUnits="userSpaceOnUse">
<stop stop-color="#B771E5"/>
<stop offset="1" stop-color="#ED9292"/>
</linearGradient>
</defs>
</svg>
