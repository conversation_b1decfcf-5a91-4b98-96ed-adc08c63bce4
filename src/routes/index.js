
import { BrowserRouter,Routes, Route } from "react-router-dom";
import { Index } from '../pages/index';
import { NotFound } from '../pages/NotFound';
import { TermsOfUse} from '../pages/TermsOfUse';
import { PrivacyPolicies} from '../pages/PrivacyPolicies';

export function RoutesApp() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Index />} />
        <Route path="/terms" element={<TermsOfUse />} />
        <Route path="/privacy" element={<PrivacyPolicies />} />
        <Route path="*" element={<NotFound />} />
      </Routes>
    </BrowserRouter>
  )
}