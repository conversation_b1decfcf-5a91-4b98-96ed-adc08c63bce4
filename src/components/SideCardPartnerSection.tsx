export function SideCard({
  icon,
  iconBgClass = "",
  title,
  description,
  positionClass,
}: {
  icon: string;
  iconBgClass?: string;
  title: string;
  description: string;
  positionClass: string;
}) {
  return (
    <div
      className={`absolute ${positionClass} max-w-[219px] transform translate-y-1/2 rounded-xl bg-white p-4 shadow-lg backdrop-blur-sm fade-up`}
    >
      <div className="flex h-full flex-col justify-between gap-8">
        <div
          className={`flex items-center justify-center h-[42px] w-[42px] rounded-full ${iconBgClass}`}
          style={{ animation: "pulseScale 3s ease-in-out infinite" }}
        >
          <img src={icon} alt="" className="h-[42px] w-[42px]" />
        </div>
        <div className="flex flex-col gap-2">
          <p className="text-base text-transparent bg-clip-text bg-gradient-pink-purple leading-tight">
            {title}
          </p>
          <p className="text-sm font-light leading-snug text-slate_gray">
            {description}
          </p>
        </div>
      </div>
    </div>
  );
}