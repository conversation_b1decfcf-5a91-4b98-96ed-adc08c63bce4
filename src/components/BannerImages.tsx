interface BannerImagesProps {
  mobileSrc: string;
  desktopSrc: string;
  alt: string;
}

export function BannerImages({ mobileSrc, desktopSrc, alt }: BannerImagesProps) {
  return (
    <>
      <img
        src={mobileSrc}
        alt={alt}
        className="rounded-2xl w-full md:hidden fade-up"
      />
      <img
        src={desktopSrc}
        alt={alt}
        className="hidden rounded-2xl md:block fade-up"
      />
    </>
  );
}
