import { PulseIcon } from "./PulseIcon";
import beerCup from "../assets/svg/beerCup.svg";
import cheese from "../assets/svg/cheese.svg";
import milk from "../assets/svg/milk.svg";
import spray from "../assets/svg/spray.svg";

export function TermsOfUseComponent() {
  return (
    <div className="max-w-4xl mx-auto px-4 py-12 text-dark_warm_gray">
      <div className="flex flex-col items-center gap-4">
        <div className="flex items-center justify-center gap-0">
          <PulseIcon src={beerCup} delay="0s" className="z-10" />
          <PulseIcon src={cheese} delay="0.3s" className="-ml-2 z-20" />
          <PulseIcon src={milk} delay="0.6s" className="-ml-2 z-30" />
          <PulseIcon src={spray} delay="0.9s" className="-ml-2 z-40" />
        </div>
        <h1 className="text-2xl font-semibold text-dark_warm_gray">TERMOS E CONDIÇÕES DE USO PARA OS USUÁRIOS</h1>
        <p className="text-base text-dark_warm_gray font-light">Última atualização: 24/07/2025</p>
      </div>

      <section className="mt-20 flex flex-col gap-12 leading-relaxed text-dark_warm_gray text-xl">
        <div className="flex flex-col gap-4">
          <h2 className="font-semibold">1. OBJETO</h2>
          <p>
            A presente Plataforma digital tem como finalidade conectar usuários a estabelecimentos comerciais, atuando como intermediadora na divulgação, venda e pagamento de produtos por meio do ambiente digital disponibilizado.
          </p>
        </div>

        <div className="flex flex-col gap-4">
          <h2 className="font-semibold">2. Natureza da Plataforma</h2>
          <p>
            A Plataforma atua exclusivamente como intermediadora na divulgação de produtos e na facilitação do processo de compra e pagamento. Não somos fabricantes, fornecedores, nem vendedores diretos dos produtos anunciados.
          </p>
        </div>

        <div className="flex flex-col gap-4">
          <h2 className="font-semibold">3. Responsabilidade pela Entrega</h2>
          <p>
            Toda a logística de entrega e envio dos produtos é de responsabilidade exclusiva dos estabelecimentos. A Plataforma não se responsabiliza por atrasos, falhas na entrega, danos, defeitos, vícios ou divergências nos produtos adquiridos.
          </p>
        </div>

        <div className="flex flex-col gap-4">
          <h2 className=" font-semibold">4. Relação entre Usuários e Estabelecimentos Comerciais</h2>
          <p>
            Ao realizar uma compra, o usuário reconhece que a relação contratual de consumo se estabelece diretamente com o estabelecimento comercial do anunciante, sendo este o único responsável por: emitir nota fiscal (quando aplicável), prestar suporte pós-venda, responder por vícios ou defeitos nos produtos, realizar trocas ou devoluções, conforme a legislação vigente.
          </p>
        </div>

        <div className="flex flex-col gap-4">
          <h2 className=" font-semibold ">5. Coleta e Proteção de Dados Pessoais</h2>
          <p>
            A Plataforma realiza a coleta de dados pessoais dos usuários de forma limitada ao necessário para o funcionamento do sistema. Os dados são tratados em conformidade com a LGPD (Lei Geral de Proteção de Dados) e utilizados exclusivamente para o funcionamento adequado da Plataforma.
          </p>
        </div>

        <div className="flex flex-col gap-4">
          <h2 className="font-semibold">6. Limitação de Responsabilidade da Plataforma</h2>
          <p>
            A Plataforma não garante a veracidade, qualidade, adequação, segurança ou legalidade dos produtos divulgados, tampouco se responsabiliza por quaisquer prejuízos decorrentes da relação entre usuários e os estabelecimentos comerciais.
          </p>
        </div>

        <div className="flex flex-col gap-4">
          <h2 className="font-semibold">7. Aceite dos Termos</h2>
          <p>
            Ao acessar e utilizar a Plataforma, o usuário declara que leu, compreendeu e concorda integralmente com os presentes Termos e Condições de Uso.
          </p>
        </div>
      </section>

    </div>
  );
}
