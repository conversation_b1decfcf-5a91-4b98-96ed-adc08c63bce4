import logoKonoha from "@/assets/svg/logoKonoha.svg";
import starLight from "@/assets/svg/star-light.svg";
import breadProduct from "@/assets/breadProduct.png";
import waterProduct from "@/assets/svg/waterProduct.svg";
import arrowDown from "@/assets/svg/arrow-down.svg";

export function PartnerHighlightCard() {
  return (
    <div className="hidden md:flex mt-8 flex-col gap-3 rounded-xl bg-white p-4 shadow-lg md:absolute md:top-[37%] lg:top-[75%] lg:left-[52%] lg:mt-0 lg:w-3/4 lg:-translate-x-1/2 lg:-translate-y-1/2 max-w-[358px]">
      <div className="flex items-center justify-between gap-3">
        <div className="flex items-center gap-4">
          <img src={logoKonoha} alt="Logo Supermercado Konoha" className="h-10 w-10 rounded-full" />
          <span className="text-sm font-medium text-gray-900">Supermercado Konoha</span>
        </div>
        <div className="flex items-center gap-1">
          <img src={starLight} alt="Estrela" className="h-4 w-4" style={{ animation: "pulseScale 3s ease-in-out infinite" }} />
          <span className="text-[10px] font-medium text-transparent bg-gradient-pink-purple bg-clip-text">
            Mais completo
          </span>
        </div>
      </div>

      <div className="flex items-center justify-between gap-2">
        <div className="flex items-center">
          <div className="z-10 rounded-full bg-off_white p-1 ring-2 ring-white">
            <img src={breadProduct} alt="Produto óleo" className="h-6 w-6" />
          </div>
          <div className="-ml-2 z-20 rounded-full bg-off_white p-1 ring-2 ring-white">
            <img src={waterProduct} alt="Produto água" className="h-6 w-6" />
          </div>
          <div className="-ml-2 z-30 flex h-8 w-8 items-center justify-center rounded-full bg-off_white text-[11px] font-medium text-slate_gray ring-2 ring-white font-inter">
            28
          </div>
        </div>
        <div className="flex flex-col items-start">
          <span className="text-[10px] text-dark_warm_gray">Total do pedido</span>
          <div className="flex items-center gap-1">
            <span className="text-base font-bold text-dark_warm_gray font-inter">R$ 102,00</span>
            <img src={arrowDown} alt="" />
          </div>
        </div>
      </div>
    </div>
  );
}
