
import ShoppingCard from '@/assets/svg/shopingCart.svg';
import { Link } from 'react-router-dom';


type HeaderProps = {
  fixed?: boolean;
  hasShadow?: boolean
};

export function Header({ fixed = false, hasShadow = false }: HeaderProps) {

  return (
    <header className={`w-full ${fixed ? 'lg:fixed' : ''} top-0 z-50 bg-white lg:transition-shadow duration-300 ${hasShadow ? 'lg:shadow' : ''}`}>
      <div className="container mx-auto px-4 py-6 flex items-center justify-center md:justify-start lg:justify-between relative">
        <Link to="/">
          <div className="flex items-center gap-1">
            <img src={ShoppingCard} alt="Shopping Cart" className="w-8 h-8" />
            <span className="text-[32px] font-medium text-medium_warm_gray text">izy</span>
          </div>
        </Link >

        <nav className="hidden md:flex items-center gap-6 md:ml-auto lg:ml-0 lg:absolute lg:left-1/2 lg:transform lg:-translate-x-1/2">
          <a
            href="https://docs.google.com/forms/d/e/1FAIpQLSfORPDKLiq_5WzWRDXCBeSRUBxt-Na6IfhdPAdssVBOktwG8Q/viewform"
            target="_blank"
            rel="noopener noreferrer"
            className="text-roboto font-normal text-medium_warm_gray relative after:absolute after:left-0 after:bottom-0 after:w-0 after:h-[2px] after:bg-gradient-pink-purple after:transition-all after:duration-300 hover:after:w-full"
          >
            Seja nosso parceiro
          </a>
          <a
            href="mailto:<EMAIL>"
            className="text-roboto font-normal text-medium_warm_gray relative after:absolute after:left-0 after:bottom-0 after:w-0 after:h-[2px] after:bg-gradient-pink-purple after:transition-all after:duration-300 hover:after:w-full">
            Suporte
          </a>
        </nav>

        <div className="hidden lg:flex">
          <button className="bg-gradient-pink-purple text-white px-8 py-4 rounded-full text-sm hover:bg-[linear-gradient(90deg,_#9B5BC2_0%,_#C67474_100%)]">
            Quero fazer minhas compras com a Izy
          </button>
        </div>
      </div>
    </header>


  );
};

