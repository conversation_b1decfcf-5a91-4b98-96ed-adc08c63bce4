import { CashbackCard } from "./CashbackCardProps";
import { OrderStatusCard } from "./OrderStatusCardProps";
import thirdBannerImage from "../assets/thirdBanner.png";

export function CashbackSection() {
  if (typeof window !== "undefined") {
    setTimeout(() => {
      const elements = document.querySelectorAll(".fade-up-cashback");
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              entry.target.classList.add("animate");
              observer.unobserve(entry.target);
            }
          });
        },
        { threshold: 0.3 }
      );

      elements.forEach((el) => observer.observe(el));
    }, 100);
  }

  return (
    <section className="pt-16 bg-gradient-to-br from-background to-accent/20">
      <style>{`
        @keyframes fadeUpCashback {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .fade-up-cashback {
          opacity: 0;
          transform: translateY(20px);
          transition: opacity 0.8s ease-out, transform 0.8s ease-out;
        }
        .fade-up-cashback.animate {
          animation: fadeUpCashback 0.8s ease forwards;
        }
      `}</style>

      <div className="container mx-auto px-4">
        <div className="relative max-w-4xl mx-auto w-full">
          <img
            src={thirdBannerImage}
            alt="Mulher usando o app Izy no supermercado"
            className="max-w-[600px] w-full rounded-2xl mx-auto"
          />

        
          <div className="lg:hidden absolute left-1/2 transform -translate-x-1/2 flex items-end justify-center w-full bottom-[-220px] max-[430px]:bottom-[-260px] sm:bottom-[-200px]">
            <div className="flex flex-col gap-4 p-4 pointer-events-auto">
              <OrderStatusCard />
              <CashbackCard />
            </div>
          </div>

          
          <div className="hidden lg:flex lg:absolute lg:top-[25%] lg:left-[-5%] lg:transform lg:-translate-y-1/2 lg:w-[350px]">
            <OrderStatusCard className="w-full" />
          </div>

          
          <div className="hidden lg:flex lg:absolute lg:bottom-[-5%] lg:right-[8%] lg:transform lg:-translate-y-1/2 lg:w-[280px]">
            <CashbackCard className="w-full" />
          </div>
        </div>
      </div>
    </section>
  );
}
