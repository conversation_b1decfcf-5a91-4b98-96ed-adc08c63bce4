type SideCardProps = {
  icon: string;
  iconBgClass?: string;
  title: string;
  description: string;
  positionClass?: string; // usado só para desktop cards posicionados com absolute
  isRoundedIcon?: boolean;
};

export function SideCard({
  icon,
  iconBgClass = "",
  title,
  description,
  positionClass = "",
  isRoundedIcon = false,
}: SideCardProps) {
  return (
    <div
      className={`bg-white p-4 rounded-xl shadow-lg backdrop-blur-sm flex flex-col justify-between gap-6 fade-up ${
        positionClass ? `absolute ${positionClass} transform translate-y-1/2` : ""
      }`}
      style={{ minWidth: positionClass ? "219px" : undefined, minHeight: positionClass ? "188px" : undefined }}
    >
      <div
        className={`flex items-center justify-center h-[42px] w-[42px] ${isRoundedIcon ? "rounded-full" : ""} ${iconBgClass}`}
        style={{ animation: "pulseScale 3s ease-in-out infinite" }}
      >
        <img src={icon} alt="" className="h-[42px] w-[42px]" />
      </div>
      <div className="flex flex-col gap-2">
        <p className="text-[14px] text-transparent bg-clip-text bg-gradient-pink-purple leading-tight">{title}</p>
        <p className="text-[12px] font-light leading-snug text-slate_gray text-justify">{description}</p>
      </div>
    </div>
  );
}
