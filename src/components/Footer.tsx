import appleIcon from '../assets/svg/apple_icon.svg';
import shopingCart from '../assets/svg/shopingCart.svg';
import linkedinIcon from '../assets/svg/linkedin_icon.svg';
import instagramIcon from '../assets/svg/instagram_icon.svg';
import googleStoreIcon from '../assets/svg/google_store_icon.svg';
import { Link } from 'react-router-dom';

export function Footer() {
  return (
    <footer className="bg-off_white w-full mt-36 px-4 py-6 flex flex-col items-center text-center lg:text-left lg:px-20 lg:py-12 gap-16">


      <div className="w-full flex flex-row items-center gap-6 lg:items-start lg:justify-between">


        <div className="flex flex-wrap items-center w-full gap-4 ">

          <div className="flex items-center  gap-1 ">
            <img src={shopingCart} alt="Logo Izy" className="h-16 flex-shrink-0" />
            <h1 className="text-6xl text-medium_warm_gray font-medium font-jakarta">izy</h1>
          </div>


          <div className="hidden lg:flex text-center w-3/4">
            <p className="text-dark_warm_gray text-[20px] font-light ">
              O futuro das compras é local, digital e inteligente. Venha com a gente!
            </p>
          </div>
        </div>


        <div className="flex justify-end lg:justify-end gap-4 w-full lg:w-1/3">
          <a
            href="https://www.instagram.com/izy.mercado"
            target="_blank"
            rel="noopener noreferrer"
            className="rounded-full border border-gray_light p-2 cursor-pointer hover:shadow-lg"
          >
            <img src={instagramIcon} alt="Instagram" className="w-6 h-6" />
          </a>
          <a
            href="https://www.linkedin.com/company/izy-mercado"
            target="_blank"
            rel="noopener noreferrer"
            className="rounded-full border border-gray_light p-2 cursor-pointer hover:shadow-lg"
          >
            <img src={linkedinIcon} alt="Linkedin" className="w-6 h-6" />
          </a>
        </div>

      </div>


      <div id="bottomFooter" className="w-full">
        <div className="w-full flex flex-col items-center justify-center md:justify-between md:flex-row gap-4">

          <div className="flex flex-wrap justify-center gap-x-4 gap-y-2 w-full max-w-[500px] mt-2 mb-6">

            <button
              onClick={(e) => {
                e.preventDefault();
                window.scrollTo({ top: 0, behavior: "smooth" });
                if (window.innerWidth >= 1024) {
                  window.open(
                    "https://docs.google.com/forms/d/e/1FAIpQLSfORPDKLiq_5WzWRDXCBeSRUBxt-Na6IfhdPAdssVBOktwG8Q/viewform",
                    "_blank"
                  );
                } else {
                  window.location.href = "mailto:<EMAIL>";
                }
              }}
              className="text-xs px-8 py-2 font-medium border rounded-full border-gray_soft text-dark_warm_gray font-inter hover:shadow-lg active:shadow-none transition-all"
            >
              <span className="lg:hidden">Suporte</span>
              <span className="hidden lg:inline">Seja um parceiro</span>
            </button>

            <Link to="/terms">
              <button
                onClick={() =>
                  window.scrollTo({ top: 0, behavior: "smooth" })
                }
                className="text-xs px-7 py-2 font-medium border rounded-full border-gray_soft text-dark_warm_gray font-inter hover:shadow-lg"
              >
                Termos de uso
              </button>
            </Link>

            <Link to="/privacy">
              <button
                onClick={() =>
                  window.scrollTo({ top: 0, behavior: "smooth" })
                }
                className="text-xs px-8 py-2 font-medium border rounded-full border-gray_soft text-dark_warm_gray font-inter hover:shadow-lg"
              >
                Privacidade
              </button>
            </Link>

          </div>


          <div id="appButtons" className="flex gap-4 w-full md:justify-center lg:justify-end">
            <a
              href="#"
              className="flex-1 gap-[32px] p-4 border rounded-lg  text-center flex flex-col items-start text-start md:max-w-[148px] hover:shadow-lg"
            >
              <img src={appleIcon} alt="Apple Store" className="w-8 h-8" />
              <p className="text-xs text-slate_gray font-light">
                Disponível na<br /><strong className="text-dark_warm_gray text-base">Apple Store</strong>
              </p>
            </a>
            <a
              href="#"
              className="flex-1 gap-[32px] p-4 border rounded-lg text-center flex flex-col items-start text-start md:max-w-[148px] hover:shadow-lg"
            >
              <img src={googleStoreIcon} alt="Google Play" className="w-8 h-8" />
              <p className="text-xs text-slate_gray font-light">
                Disponível na<br /><strong className="text-dark_warm_gray text-base">Google Play</strong>
              </p>
            </a>
          </div>
        </div>


        <div className="mt-12 text-center w-full flex flex-col gap-2 items-center">
          <p className="text-dark_warm_gray font-light text-xs font-inter w-full  lg:text-sm">
            Av. Engenheiro Roberto Freire, 1962, Swaay Shopping, Loja 13, Capim Macio, Natal/RN, 59082-095
          </p>
          <p className="text-dark_warm_gray font-light text-xs font-inter lg:text-sm px-4">
            © 2025, Izy Mercado. Todos os direitos reservado · 61.134.691/0001-00
          </p>
        </div>
      </div>
    </footer>
  );
}
