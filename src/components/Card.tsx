import shoppingCart from "../assets/svg/shopingCart.svg";
export function Card({ icon, label }: { icon: string; label: string }) {
  return (
    <div className="min-w-[160px] lg:w-full p-4 border rounded-xl shadow-sm bg-white text-center flex flex-col items-start gap-8 ">
      <img src={icon} alt={label} className="w-[55px]" />
      <div className="flex justify-center items-center gap-1">
        <img src={shoppingCart} alt="" className="w-6" />
        <p className="text-base text-medium_warm_gray font-medium">{label}</p>
      </div>
    </div>
  );
}
