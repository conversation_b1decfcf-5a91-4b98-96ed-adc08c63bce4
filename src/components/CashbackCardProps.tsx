import  ShoppingCart  from '../assets/svg/shopingCart.svg';

interface CashbackCardProps {
  className?: string;
}

export function CashbackCard({ className = "" }: CashbackCardProps) {
  return (
    <div className={`flex flex-col gap-4 rounded-xl bg-off_white p-4 fade-up-cashback ${className}`}>
      <img src={ShoppingCart} alt="Ícone Cashback" className="w-[24px]" />
      <div className="flex flex-col gap-2">
        <p className="font-bold text-sm text-medium_warm_gray font-inter">
          Você ganhou R$ 12,00 de cashback!
        </p>
        <p className="text-xs text-medium_warm_gray font-inter">
          Você vai receber um PIX na conta onde você pagou o seu pedido
        </p>
      </div>
    </div>
  );
}
