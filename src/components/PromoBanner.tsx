export function PromoBanner() {
  return (
    <div
      className="w-full mx-auto bg-gradient-pink-purple"
      style={{ animation: 'fadeIn 1.5s ease forwards' }}
    >
      <style>{`
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
      `}</style>

      <div className="container mx-auto px-4 py-6 flex flex-col gap-2 xs:gap-4 md:flex-row md:items-center relative">
        
        
        <div className="flex items-center justify-between gap-4 text-white w-full md:w-3/4">
          <span className="font-light text-base xs:text-xl md:text-xl md:text-2xl">
            QUER GANHAR <span className="font-bold">R$50</span>?
          </span>

          
          <span className="font-light text-base xs:text-xl  md:text-xl md:text-2xl text-roboto md:absolute md:left-1/2 md:-translate-x-1/2">
            Use o cupom: <span className="font-bold">50IZY</span>
          </span>
        </div>

     
        <div className="text-white text-start md:text-end md:w-1/4">
          <span className="text-[8px] xs:text-xs font-light md:text-sm md:hidden">
            *Para compras acima de R$ 500
          </span>
          <span className="hidden md:block text-xs text-roboto font-light md:text-sm">
            *Consulte o regulamento
          </span>
        </div>
      </div>
    </div>
  );
}
