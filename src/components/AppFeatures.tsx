import card_iphone1 from "../assets/card_iphone1.png";
import card_iphone2 from "../assets/card_iphone2.png";
import card_iphone3 from "../assets/card_iphone3.png";
import arrowUp from "../assets/svg/arrow-up.svg";

export function AppFeaturesSection() {
  return (
    <div>
      <div className="w-full overflow-x-auto px-4 lg:overflow-visible lg:px-0 mt-16 lg:mt-[160px] scrollbar-none">
        <div
          className="flex gap-4 lg:gap-8 lg:justify-center"
          style={{ scrollSnapType: "x mandatory" }}
        >
          {[card_iphone1, card_iphone2, card_iphone3].map((src, index) => (
            <img
              key={index}
              src={src}
              alt={`Card iPhone ${index + 1}`}
              className="w-full max-w-[300px] lg:max-w-[329px] rounded-2xl "
              style={{ scrollSnapAlign: "center" }}
            />
          ))}
        </div>
      </div>
      :
      <div className="flex fixed bottom-16 right-[22px] lg:right-[42px] z-[9999] pointer-events-auto">
        <img
          src={arrowUp}
          alt="Voltar ao topo"
          className="cursor-pointer w-12 h-12 lg:w-16 lg:h-16"
          onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
        />
      </div>

    </div>
  );
}
