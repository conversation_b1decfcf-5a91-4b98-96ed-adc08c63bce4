import { PulseIcon } from "./PulseIcon";
import { GradientTextBlock } from "./GradientTextBlock";
import { OverlayTag } from "./OverlayTag";
import { BannerImages } from "./BannerImages";
import { SideCard } from "./SideCardPartnerSection";
import { MobileCard } from "./MobileCard";

import beerCup from "../assets/svg/beerCup.svg";
import cheese from "../assets/svg/cheese.svg";
import milk from "../assets/svg/milk.svg";
import spray from "../assets/svg/spray.svg";
import ShoppingCart from "@/assets/svg/shopingCart.svg";
import dollarColor from "../assets/dollar.png";
import firstBannerImage from "../assets/bannerMobile.png";
import bigBannerImage from "../assets/bigBanner.png";

export function HeroSection() {
  return (
    <section className="pt-16 bg-gradient-to-br from-background to-accent/20" style={{ animation: 'fadeIn 1.5s ease forwards' }}>
      <style>{`
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
        @keyframes pulseScale {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.1); }
        }
      `}</style>

      <div className="container mx-auto px-4 text-center mb-20">
        <div className="flex items-center justify-center gap-0">
          <PulseIcon src={beerCup} delay="0s" className="z-10" />
          <PulseIcon src={cheese} delay="0.3s" className="-ml-2 z-20" />
          <PulseIcon src={milk} delay="0.6s" className="-ml-2 z-30" />
          <PulseIcon src={spray} delay="0.9s" className="-ml-2 z-40" />
        </div>

        <GradientTextBlock title="Seu mercado," highlight="fácil e inteligente" />

        <button
         className="tracking-wide text-white px-8 py-4 rounded-full text-sm bg-[linear-gradient(90deg,_#B771E5_0%,_#ED9292_100%)] hover:bg-[linear-gradient(90deg,_#9B5BC2_0%,_#C67474_100%)]"
          style={{ animation: 'fadeIn 2s ease forwards', animationDelay: '1.2s' }}
        >
          Quero fazer minhas compras com a Izy
        </button>
      </div>


      <div className="relative mx-auto max-w-[800px] w-full flex flex-col items-center px-4">
        <BannerImages mobileSrc={firstBannerImage} desktopSrc={bigBannerImage} alt="banner" />

        <OverlayTag />

        <div className="hidden lg:block">
          <SideCard
            icon={ShoppingCart}
            title="Sua lista de compras em minutos!"
            description="Em até 5min, você cria sua lista, compara os preços e recebe em casa. Tudo num só app"
            positionClass="bottom-[40%] left-[-10%]"
          />
          <SideCard
            icon={dollarColor}
            iconBgClass="bg-primary/20"
            title="Receba dinheiro de volta"
            description="Na Izy, você ganha comodidade e ainda ganha cashback"
            positionClass="bottom-[10%] right-[-13%]"
          />
        </div>

        <div className="lg:hidden w-full ">
          <div className="w-full mt-4 flex justify-between md:justify-center  gap-4 md:gap-20 max-[430px]:gap-3">
            <MobileCard
              icon={ShoppingCart}
              title="Mais visibilidade para o seu negócio"
              description="Seja parceiro da Izy e comece a vender mais, com nossa tecnologia"
            />
            <MobileCard
              icon={dollarColor}
              iconBgClass="bg-primary/20"
              title="Relatórios e dados de performance"
              description="Com a Izy, você sabe exatamente o que vender e por quanto vai vender"
            />
          </div>
        </div>
      </div>
    </section>
  );
}
