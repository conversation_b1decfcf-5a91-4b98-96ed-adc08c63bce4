export function MobileCard({
  icon,
  iconBgClass = "",
  title,
  description,
}: {
  icon: string;
  iconBgClass?: string;
  title: string;
  description: string;
}) {
  return (
    <div className="rounded-xl bg-white p-4 shadow-lg backdrop-blur-sm  max-[430px]:h-[138px] max-[430px]:p-3 fade-up">
      <div className="flex h-full flex-col justify-between gap-6 max-[430px]:gap-3">
        <div
          className={`flex items-center justify-center h-[42px] w-[42px] max-[430px]:h-[32px] max-[430px]:w-[32px] rounded-full ${iconBgClass}`}
          style={{ animation: "pulseScale 3s ease-in-out infinite" }}
        >
          <img src={icon} alt="" className="h-full w-full" />
        </div>
        <div className="flex flex-col gap-2 max-[430px]:gap-1">
          <p className="text-[14px] max-[430px]:text-[12px] text-transparent bg-clip-text bg-gradient-pink-purple leading-tight">
            {title}
          </p>
          <p className="text-[12px] max-[430px]:text-[10px] font-light leading-snug text-slate_gray ">
            {description}
          </p>
        </div>
      </div>
    </div>
  );
}