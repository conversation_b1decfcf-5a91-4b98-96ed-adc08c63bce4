import logoKonoha from '../assets/svg/logoKonoha.svg'

interface OrderStatusCardProps {
  className?: string;
}

export function OrderStatusCard({ className = "" }: OrderStatusCardProps) {
  return (
    <div className={` rounded-xl bg-off_white p-4 fade-up-cashback ${className}`}>
      <div className="flex items-center gap-4">
        <img
          src={logoKonoha}
          alt="Logo Mercado"
          className="w-10 h-10 rounded-full"
        />
        <div>
          <p className="text-[11px] text-slate_gray font-inter">Mercado</p>
          <p className="font-medium font-inter text-dark_warm_gray">Super Econômico</p>
        </div>
      </div>
      <div className="flex justify-between items-center mt-4 text-sm font-medium">
        <div className="w-full h-1 bg-sage_green rounded-full mr-1" />
        <div className="w-full h-1 bg-sage_green rounded-full mx-1" />
        <div className="w-full h-1 bg-sage_green rounded-full mx-1" />
        <div className="w-full h-1 bg-sage_green rounded-full mx-1" />
      </div>
      <div className="flex items-center justify-start gap-1 mt-2">
        <span className="font-inter text-[10px] text-slate_gray leading-110%">15:25</span>
        <span className="w-1 text-light_gray">•</span>
        <span className="text-xs font-inter text-dark_warm_gray">Concluído</span>
      </div>
    </div>
  );
}
