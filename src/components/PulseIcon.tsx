type PulseIconProps = {
  src: string;
  alt?: string;
  delay?: string;
  className?: string;
};

export function PulseIcon({ src, alt = "", delay = "0s", className = "" }: PulseIconProps) {
  return (
    <div
      className={`bg-off_white p-[10px] rounded-full ring-2 ring-white ${className}`}
      style={{ animation: 'pulseScale 3s ease-in-out infinite', animationDelay: delay }}
    >
      <img src={src} alt={alt} className="w-6" />
    </div>
  );
}
