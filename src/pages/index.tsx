
import { Header } from "@/components/Header";
import { PromoBanner } from "../components/PromoBanner";
import { HeroSection } from "../components/HeroSection";
import { AppFeaturesSection } from "../components/AppFeatures";
import { AppDownloadSection } from "../components/AppDownload";
import { PartnerSection } from "../components/PartnerSection";
import { CashbackSection } from "../components/CashbackSection";
import { Testimonials } from "../components/Testimonials";
import { Footer } from "../components/Footer";
import { useEffect, useState } from "react";
export function Index() {

  const [hasShadow, setHasShadow] = useState(false);

  useEffect(() => {
    function handleScroll() {
      setHasShadow(window.scrollY > 0);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);


  return (
    <div className="min-h-screen bg-background">
      <Header hasShadow={hasShadow} fixed />
      <div className="lg:pt-[96px]">
        <PromoBanner />
        <HeroSection />
        <AppFeaturesSection />
        <AppDownloadSection />
        <PartnerSection />
        <CashbackSection />
        <Testimonials />
        <Footer />
      </div>

    </div>
  );
};


