
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      backgroundImage: {
        'gradient-pink-purple': 'linear-gradient(90deg, #B771E5 0%, #ED9292 100%)',
      },
      colors: {
        medium_warm_gray: "#858082",
        dark_warm_gray: "#514D4F",
        slate_gray: "#666666",
        off_white: '#F8F8F7',
        sage_green: '#85B29A',
        cloud_gray: '#CCCCCC',
        pale_gray: '#F2F2F2',
        gray_light: '#E5E5E5',
        gray_soft: '#EBEBEB',
      },
      screens: {
        'xs': '450px',
        // => @media (min-width: 400px) { ... }
        'sm': '640px',
        // => @media (min-width: 640px) { ... }

        'md': '768px',
        // => @media (min-width: 768px) { ... }

        'lg': '1024px',
        // => @media (min-width: 1024px) { ... }

        'xl': '1280px',
        // => @media (min-width: 1280px) { ... }

        '2xl': '1536px',
        // => @media (min-width: 1536px) { ... }

      },
      fontFamily: {
        poppins: ['Poppins', 'sans-serif'],
        roboto: ['Roboto', 'sans-serif'],
        inter: ['Inter', 'sans-serif'],
        jakarta: ['Plus Jakarta Sans', 'sans-serif'],
      },
      boxShadow: {
        'custom-soft': '0px 4px 20px rgba(81, 77, 79, 0.10)',
      },

    },
  },
  plugins: [],
}

