{
  "compilerOptions": {
    "baseUrl": "./src",
    "paths": {
      "@/*": [
        "*"
      ]
    },
    "target": "ESNext",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "moduleResolution": "Node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "jsx": "react-jsx",
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "skipLibCheck": true,
  },
  "include": [
    "src", "vite-env.d.ts",
  ],
  "exclude": [
    "node_modules"
  ]
}